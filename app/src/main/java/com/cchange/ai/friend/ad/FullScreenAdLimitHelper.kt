package com.cchange.ai.friend.ad

import com.cchange.ai.friend.biz.remoteconf.RealRemoteConf
import com.russhwolf.settings.coroutines.SuspendSettings
import kotlinx.datetime.Instant
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import kotlin.time.Duration.Companion.minutes

@Suppress("FunctionName")
object FullScreenAdLimitHelper : KoinComponent {

  private val realRemoteConf: RealRemoteConf by inject()

  private suspend fun adsConf() = realRemoteConf.adsConf() ?: AdsConf.Default

  private const val TAG = "FullScreenAdLimitHelper"

  private suspend fun MAX_SHOWS() = adsConf().fads_active_cycle_max_shows
  private suspend fun MAX_CLICKS() = adsConf().fads_active_cycle_max_clicks
  private suspend fun MAX_ACTIVE_CYCLE_DURATION() = adsConf().fads_active_cycle_minutes.minutes

  private val suspendSettings: SuspendSettings by inject()

  var displayTimes: Int = 0
    private set

  var clickTimes: Int = 0
    private set


  // invoke in TimeTickReceiver
  suspend fun calculateActiveCycle(
    now: Instant
  ) {
    if (now - latestActiveStartInstant() > MAX_ACTIVE_CYCLE_DURATION()) {
      entryNewCycle(now)
    }
  }

  fun displayOnce() {
    displayTimes++
  }

  fun clickOnce() {
    clickTimes++
  }

  suspend fun isLimit(): Boolean {
    return displayTimes > MAX_SHOWS() || clickTimes > MAX_CLICKS()
  }

  private suspend fun entryNewCycle(now: Instant) {
    displayTimes = 0
    clickTimes = 0
    setLatestActiveStartInstant(now)
  }

  // ---------------------------------------------------------------------------------------------
  private const val KEY_LATEST_ACTIVE_INSTANT = "${TAG}_latestActiveStartInstant"

  private suspend fun latestActiveStartInstant(): Instant {
    return suspendSettings
      .getLong(KEY_LATEST_ACTIVE_INSTANT, 0L)
      .let { Instant.fromEpochSeconds(it) }
  }

  private suspend fun setLatestActiveStartInstant(instant: Instant) {
    suspendSettings.putLong(KEY_LATEST_ACTIVE_INSTANT, instant.epochSeconds)
  }

}