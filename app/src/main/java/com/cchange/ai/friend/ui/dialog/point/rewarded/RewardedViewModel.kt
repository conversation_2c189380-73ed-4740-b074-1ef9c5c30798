package com.cchange.ai.friend.ui.dialog.point.rewarded

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.cchange.ai.friend.ad.interstitial.MaxInterstitialAdHelper
import com.cchange.ai.friend.ad.rewarded.MaxRewardedAdHelper
import com.cchange.ai.friend.ad.rewarded.rewardedAdFinishEventFlow
import com.cchange.ai.friend.ad.rewarded.ui.rewardedLoadingDialogTimeoutEventFlow
import com.cchange.ai.friend.biz.rating.RatingManager
import com.cchange.ai.friend.core.coroutine.AppCoroutineScope
import com.cchange.ai.friend.core.flow.safeFirst
import com.cchange.ai.friend.core.log.debugLog
import com.cchange.ai.friend.data.network.api.PointApi
import com.cchange.ai.friend.data.network.apipojocache.PointsComponentCache
import com.cchange.ai.friend.data.network.pojo.Transceiver
import com.cchange.ai.friend.data.network.pojo.point.AddPointRequest
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.viewmodel.container
import kotlin.math.max

class RewardedViewModel(
  private val args: RewardedArgs,
  private val pointApi: PointApi,
  private val pointsComponentCache: PointsComponentCache,
  private val maxRewardedAdHelper: MaxRewardedAdHelper,
  private val appCoroutineScope: AppCoroutineScope,
  maxInterstitialAdHelper: MaxInterstitialAdHelper,
) : ViewModel(), ContainerHost<RewardedViewState, RewardedSideEffect> {

  override val container: Container<RewardedViewState, RewardedSideEffect> =
    container(RewardedViewState())

  init {
    appCoroutineScope.launch(Dispatchers.Default) {
      maxInterstitialAdHelper.tryToLoadAd()
    }

    rewardedLoadingDialogTimeoutEventFlow.onEach {
      debugLog("rewardedLoadingDialogTimeoutEventFlow.onEach")
      doClickBoostPointsMultiply(false)
    }.launchIn(viewModelScope)

    rewardedAdFinishEventFlow.onEach {
      intent {
        reduce { state.copy(processing = true) }

        val pointResult = pointApi
          .addPoint(
            Transceiver(
              AddPointRequest(
                type = args.awardPointsType,
                times = max(args.awardPointsTimes - 1, 1)
              )
            )
          )
          .safeFirst()
          ?.jsonData
          ?.data

        if (pointResult == null) {
          postSideEffect(RewardedSideEffect.Toast("Please check your network connectivity"))
          reduce { state.copy(processing = false) }
        } else {
          pointsComponentCache.points.emit(pointResult.point)
          reduce { state.copy(processing = false, boostPointsMultiplySuccessful = true) }
        }
      }
    }.launchIn(viewModelScope)
  }

  fun onClickBoostPointsMultiply() = intent {
    doClickBoostPointsMultiply()
    maxRewardedAdHelper.tryToShowAdWithLoadingDialog(
      onAdLimit = {
        doClickBoostPointsMultiply(false)
      }
    )
  }

  private fun doClickBoostPointsMultiply(clicked: Boolean = true) = intent {
    reduce {
      state.copy(hasClickBoostPointsMultiply = clicked)
    }
  }

  override fun onCleared() {
    appCoroutineScope.launch {
      if (args.from == RewardedFrom.Profile) {
        RatingManager.setCanRatting(true)
        delay(300)
        RatingManager.tryToOpenReviews()
      }
    }
    super.onCleared()
  }
}