package com.cchange.ai.friend.ad

import android.app.Activity
import android.app.Application
import android.os.Bundle
import com.cchange.ai.friend.IAIGlobalNavigator
import com.cchange.ai.friend.MainActivity
import com.cchange.ai.friend.biz.bi.BiReporter
import com.cchange.ai.friend.biz.bi.reportPageOnStartEvent
import com.cchange.ai.friend.core.coroutine.AppCoroutineScope
import com.cchange.ai.friend.core.log.debugLog
import com.cchange.ai.friend.ui.screen.splash.SplashArgs
import com.cchange.ai.friend.ui.screen.splash.SplashNode
import com.roudikk.guia.extensions.currentKey
import com.roudikk.guia.extensions.push
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject

private data class ActivityLifecycleCounter(
  var createdCounter: Int = 0,
  var startedCounter: Int = 0,
  var resumedCounter: Int = 0,
  var pausedCounter: Int = 0,
  var stoppedCounter: Int = 0,
  var saveInstanceStateCounter: Int = 0,
  var destroyedCounter: Int = 0,
)

object AdAppActivityLifecycleCallbacks : Application.ActivityLifecycleCallbacks, KoinComponent {

  private var _mainActivityLifecycleCounter = ActivityLifecycleCounter()
  private val _mainActivityClassName = MainActivity::class.java.name

  private val appCoroutineScope: AppCoroutineScope by inject()

  private val splashController: AppOpenSplashController by inject()
  private val biReporter: BiReporter by inject()

  override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
    if (_mainActivityClassName == activity.javaClass.name) {

      _mainActivityLifecycleCounter.createdCounter++
    }
  }

  override fun onActivityStarted(activity: Activity) {
    if (_mainActivityClassName == activity.javaClass.name) {
      debugLog("AutoLaunchSplashAdScreenRegistrar _mainActivityLifecycleCounter: $_mainActivityLifecycleCounter")

      if (
        _mainActivityLifecycleCounter.startedCounter > 0
        && _mainActivityLifecycleCounter.createdCounter > _mainActivityLifecycleCounter.destroyedCounter
      ) {
        appCoroutineScope.launch(Dispatchers.Main.immediate) {
          runCatching {
            val currentNodeIsSplash = IAIGlobalNavigator.navigator?.currentKey is SplashNode

            if (!currentNodeIsSplash && !splashController.skipSplashFlow.first()) {
              debugLog("loadAd navigate to splashAd screen _mainActivityLifecycleCounter: $_mainActivityLifecycleCounter")

              IAIGlobalNavigator.tryTransaction {
                push(
                  SplashNode(SplashArgs(false))
                )
              }
            } else {
              splashController.skipSplash(false)
            }
          }.onFailure {
            it.printStackTrace()
          }
        }
      }

      _mainActivityLifecycleCounter.startedCounter++
    } else {
      appCoroutineScope.launch(Dispatchers.Default) {
        biReporter.reportPageOnStartEvent(activity.javaClass.name)
      }
    }
  }

  override fun onActivityResumed(activity: Activity) {
    if (_mainActivityClassName == activity.javaClass.name) {
      _mainActivityLifecycleCounter.resumedCounter++
    }
  }

  override fun onActivityPaused(activity: Activity) {
    if (_mainActivityClassName == activity.javaClass.name) {
      _mainActivityLifecycleCounter.pausedCounter++
    }
  }

  override fun onActivityStopped(activity: Activity) {
    if (_mainActivityClassName == activity.javaClass.name) {
      _mainActivityLifecycleCounter.stoppedCounter++
    }
  }

  override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {
    if (_mainActivityClassName == activity.javaClass.name) {
      _mainActivityLifecycleCounter.saveInstanceStateCounter++
    }
  }

  override fun onActivityDestroyed(activity: Activity) {
    if (_mainActivityClassName == activity.javaClass.name) {
      _mainActivityLifecycleCounter = ActivityLifecycleCounter()
    }
  }
}