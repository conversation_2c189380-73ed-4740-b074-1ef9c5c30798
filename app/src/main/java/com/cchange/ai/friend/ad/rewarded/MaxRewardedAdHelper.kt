@file:Suppress("ObjectPropertyName")

package com.cchange.ai.friend.ad.rewarded

import android.app.Activity
import android.content.Context
import com.applovin.mediation.*
import com.applovin.mediation.ads.MaxRewardedAd
import com.cchange.ai.friend.BuildConfig
import com.cchange.ai.friend.ad.AdsConf
import com.cchange.ai.friend.ad.AppOpenSplashController
import com.cchange.ai.friend.ad.FullScreenAdLimitHelper
import com.cchange.ai.friend.ad.RewardedAdConf
import com.cchange.ai.friend.ad.rewarded.ui.RewardedAdLoadingFrom
import com.cchange.ai.friend.biz.analysis.AnalysisLogger
import com.cchange.ai.friend.biz.analysis.logEventAdRevenueRecord
import com.cchange.ai.friend.biz.analysis.logEventRecord
import com.cchange.ai.friend.biz.remoteconf.FirebaseRemoteConf
import com.cchange.ai.friend.core.coroutine.AppCoroutineScope
import com.cchange.ai.friend.core.datetime.nowInstant
import com.cchange.ai.friend.core.flow.EventFlow
import com.cchange.ai.friend.core.flow.send
import com.cchange.ai.friend.core.log.debugLog
import com.cchange.ai.friend.core.toast.showToast
import com.google.firebase.analytics.FirebaseAnalytics
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import kotlinx.datetime.Instant
import kotlin.time.DurationUnit
import kotlin.time.toDuration

private val _adCacheTimeoutDuration = 55.toDuration(DurationUnit.MINUTES)

class MaxRewardedAdHelper(
  private val context: Context,
  private val appCoroutineScope: AppCoroutineScope,
  private val splashController: AppOpenSplashController,
) {
  private val adConf: RewardedAdConf
    get() = FirebaseRemoteConf.adsConf()?.rewarded_ad_conf ?: AdsConf.Default.rewarded_ad_conf

  private var isShowAdLoadingDialog = false

  private var _rewardedAd: MaxRewardedAd? = null

  private val _lastCacheAdInstant = MutableStateFlow(Instant.fromEpochSeconds(0))

  private val isLoadingAdFlow = MutableStateFlow(false)

  private val currentActiveAdFrom = MutableStateFlow("")

  fun hasInit(): Boolean {
    return _rewardedAd != null
  }

  private val maxRewardedAdListener = object : MaxRewardedAdListener {
    override fun onAdLoaded(ad: MaxAd) {
      debugLog("loadAd endLoad")

      appCoroutineScope.launch(Dispatchers.Main) {
        isLoadingAdFlow.update { false }

        if (isShowAdLoadingDialog) {

          debugLog("rewardedLoadingDialogEventFlow.send(RewardedLoadingDialogEvent.ShowAdAndDismissDialog) onAdLoaded")

          tryToShowRewardedAdWithTipsDialogTimeoutJob?.cancel()
          rewardedLoadingDialogEventFlow.send(RewardedLoadingDialogEvent.ShowAdAndDismissDialog)
        }

        _lastCacheAdInstant.update { nowInstant() }
      }
    }

    override fun onAdDisplayed(ad: MaxAd) {
      debugLog("loadAd onAdDisplayed")

      context.showToast("If you leave before the ad end, you will not be rewarded.")

      splashController.skipSplash(true)

      debugLog("ad_incentive_impress")
      logEventRecord("ad_incentive_impress")

      FullScreenAdLimitHelper.displayOnce()
    }

    override fun onAdHidden(ad: MaxAd) {
      debugLog("loadAd onAdHidden")

      splashController.skipSplash(false)

      debugLog("ad_incentive_close")
      logEventRecord("ad_incentive_close")
      loadRewardedAd()

      if (isShowAdLoadingDialog) {
        rewardedAdFinishEventFlow.send(Unit)
        isShowAdLoadingDialog = false
      }
    }

    override fun onAdClicked(ad: MaxAd) {
      debugLog("ad_incentive_click")
      splashController.skipSplash(true)

      logEventRecord("ad_incentive_click")

      FullScreenAdLimitHelper.clickOnce()
    }

    override fun onAdLoadFailed(adUnitId: String, error: MaxError) {
      debugLog("loadAd onAdLoadFailed")

      appCoroutineScope.launch(Dispatchers.Main) {
        isLoadingAdFlow.update { false }


        debugLog("ad_incentive_load_failed")
        logEventRecord("ad_incentive_load_failed")

        currentActiveAdFrom.update { "" }
      }
    }

    override fun onAdDisplayFailed(ad: MaxAd, error: MaxError) {
      appCoroutineScope.launch(Dispatchers.Main) {
        debugLog("ad_incentive_display_failed")
        logEventRecord("ad_incentive_display_failed")
      }
    }

    override fun onUserRewarded(ad: MaxAd, reward: MaxReward) {
      debugLog("loadAd onUserRewarded")
      appCoroutineScope.launch(Dispatchers.Main) {
        debugLog("ad_incentive_rewarded")
        logEventRecord("ad_incentive_rewarded")
      }
    }

  }

  private val maxRewardedAdRevenueListener = MaxAdRevenueListener { ad ->
    if (!BuildConfig.DEBUG) {
      ad.let {
        logEventAdRevenueRecord("Ad_Impression_Revenue") {
          putDouble(FirebaseAnalytics.Param.VALUE, ad.revenue)
          putString(FirebaseAnalytics.Param.CURRENCY, "USD")
          putString("adNetwork", ad.networkName)
          putString("adFormat", ad.format.label)
        }

        AnalysisLogger.tryToRecordTotalAdsRevenue001(
          adFormat = ad.format.label,
          adValue = ad.revenue,
          adNetwork = ad.networkName,
          adUnitId = ad.adUnitId
        )

        logEventAdRevenueRecord(FirebaseAnalytics.Event.AD_IMPRESSION) {
          putString(FirebaseAnalytics.Param.AD_PLATFORM, "appLovin")
          putString(FirebaseAnalytics.Param.AD_SOURCE, ad.networkName)
          putString(FirebaseAnalytics.Param.AD_FORMAT, ad.format.displayName)
          putString(FirebaseAnalytics.Param.AD_UNIT_NAME, ad.adUnitId)
          putDouble(FirebaseAnalytics.Param.VALUE, ad.revenue)
          putString(FirebaseAnalytics.Param.CURRENCY, "USD") // All
        }

//        AnalysisLogger.roasReport("rewarded", ad)

        AnalysisLogger.logAdViewEvent(context, ad)

        AnalysisLogger.biLogEventAdOnPaid(
          value = ad.revenue.toFloat(),
          currency = "USD",
          precisionType = ad.revenuePrecision,
          adNetwork = ad.networkName ?: "",
          adType = ad.format.label ?: "",
        )
      }
    }
  }

  fun initIfNeed(activity: Activity) {
    if (_rewardedAd == null) {
      debugLog("loadAd init")
      _rewardedAd = MaxRewardedAd.getInstance(adConf.ad_id, activity).apply {
        setListener(maxRewardedAdListener)
        setRevenueListener(maxRewardedAdRevenueListener)
      }
    }
  }

  private fun loadRewardedAd() {
    _rewardedAd?.let {
      it.loadAd()
      debugLog("loadAd startLoad")
      isLoadingAdFlow.update { true }
    }
  }

  fun showAd() {
    _rewardedAd?.let {
      if (it.isReady) {
        debugLog("loadAd showRewardedAd")
        it.showAd()
      }
    }
  }

  suspend fun tryToLoadAd() {
    if (FullScreenAdLimitHelper.isLimit()) {
      debugLog("loadAd tryToLoadRewardedAd failed")
      debugLog("loadAd FullScreenAdLimitHelper.isLimit()")
      return
    }

    appCoroutineScope.launch(Dispatchers.Main) {
      debugLog("loadAd tryToLoadRewardedAd")

      val now = nowInstant()

      debugLog("loadAd tryToLoadRewardedAd _rewardedAd?.isReady: ${_rewardedAd?.isReady}")
      if (
        _rewardedAd?.isReady == true
        && now - _adCacheTimeoutDuration < _lastCacheAdInstant.first()
      ) { // has available ad cache
        debugLog("loadAd has available ad cache")
        return@launch
      } else {
        debugLog("loadAd no available ad cache")

        val isLoadingAd = isLoadingAdFlow.first()

        debugLog("loadAd tryToLoadRewardedAd isLoadingAd: $isLoadingAd")

        if (!isLoadingAd) {
          loadRewardedAd()
        }
      }
    }
  }

  private var tryToShowRewardedAdWithTipsDialogTimeoutJob: Job? = null
  suspend fun tryToShowAdWithLoadingDialog(
    from: String? = null,
    instantlyLoad: Boolean = true,
    rewardedAdLoadingFrom: RewardedAdLoadingFrom = RewardedAdLoadingFrom.Other,
    onAdLimit: (() -> Unit)? = null,
  ) {
    debugLog(tag = "loadAd", message = "tryToShowAdWithLoadingDialog from: $from")

    if (FullScreenAdLimitHelper.isLimit()) {
      onAdLimit?.invoke()
      debugLog(tag = "loadAd", message = "tryToShowRewardedLoadingDialog failed")
      debugLog(tag = "loadAd", message = "FullScreenAdLimitHelper.isLimit()")

      context.showToast("Too many ads viewed, please try again later")
      return
    }

    appCoroutineScope.launch(Dispatchers.Main) {
      isShowAdLoadingDialog = true

      debugLog(
        tag = "loadAd",
        message = "rewardedLoadingDialogEventFlow.send(RewardedLoadingDialogEvent.StartShow)"
      )
      rewardedLoadingDialogEventFlow.send(
        RewardedLoadingDialogEvent.StartShow(
          instantlyLoad,
          rewardedAdLoadingFrom
        )
      )

      debugLog("ad_incentive_show")
      logEventRecord("ad_incentive_show")

      val now = nowInstant()

      debugLog(tag = "loadAd", message = "_rewardedAd?.isReady -> ${_rewardedAd?.isReady}")
      if (
        (_rewardedAd?.isReady == true)
        && now - _adCacheTimeoutDuration < _lastCacheAdInstant.first()
      ) {
        delay(2000)
        debugLog(
          tag = "loadAd",
          message = "rewardedLoadingDialogEventFlow.send(RewardedLoadingDialogEvent.ShowAdAndDismissDialog)"
        )
        rewardedLoadingDialogEventFlow.send(RewardedLoadingDialogEvent.ShowAdAndDismissDialog)
      } else {

        val isLoadingAd = isLoadingAdFlow.first()
        debugLog("loadAd tryToShowRewardedAd isLoadingAd: $isLoadingAd")

        if (!isLoadingAd) {
          loadRewardedAd()
        }

        tryToShowRewardedAdWithTipsDialogTimeoutJob?.cancel()
        tryToShowRewardedAdWithTipsDialogTimeoutJob = null
        tryToShowRewardedAdWithTipsDialogTimeoutJob = launch {
          instantLoadTimeoutDelay()
          debugLog("rewardedLoadingDialogEventFlow.send(RewardedLoadingDialogEvent.LoadingTimeout)")
          rewardedLoadingDialogEventFlow.send(RewardedLoadingDialogEvent.LoadingTimeout)
        }
      }
    }
  }

  suspend fun instantLoadTimeoutDelay() {
    delay(adConf.ad_instant_load_timeout_seconds.toDuration(DurationUnit.SECONDS))
  }
}

sealed interface RewardedLoadingDialogEvent {
  data class StartShow(
    val instantlyLoad: Boolean,
    val rewardedAdLoadingFrom: RewardedAdLoadingFrom
  ) : RewardedLoadingDialogEvent

  //  data object Showing : RewardedLoadingDialogEvent
  data object LoadingTimeout : RewardedLoadingDialogEvent
  data object ShowAdAndDismissDialog : RewardedLoadingDialogEvent
}

val rewardedLoadingDialogEventFlow = EventFlow<RewardedLoadingDialogEvent>()
val rewardedAdFinishEventFlow = EventFlow<Unit>()