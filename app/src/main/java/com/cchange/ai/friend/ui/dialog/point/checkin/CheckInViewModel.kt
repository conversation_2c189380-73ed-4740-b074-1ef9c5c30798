package com.cchange.ai.friend.ui.dialog.point.checkin

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.cchange.ai.friend.ad.interstitial.MaxInterstitialAdHelper
import com.cchange.ai.friend.ad.interstitial.MaxInterstitialAdShowEvent
import com.cchange.ai.friend.ad.rewarded.MaxRewardedAdHelper
import com.cchange.ai.friend.ad.rewarded.rewardedAdFinishEventFlow
import com.cchange.ai.friend.ad.rewarded.ui.rewardedLoadingDialogTimeoutEventFlow
import com.cchange.ai.friend.biz.analysis.logEventRecord
import com.cchange.ai.friend.core.coroutine.AppCoroutineScope
import com.cchange.ai.friend.core.flow.safeFirst
import com.cchange.ai.friend.core.log.debugLog
import com.cchange.ai.friend.data.network.api.PointApi
import com.cchange.ai.friend.data.network.apipojocache.PointsComponentCache
import com.cchange.ai.friend.data.network.pojo.Transceiver
import com.cchange.ai.friend.data.network.pojo.point.AddPointRequest
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.viewmodel.container


class CheckInViewModel(
  private val pointApi: PointApi,
  private val pointsComponentCache: PointsComponentCache,
  private val maxRewardedAdHelper: MaxRewardedAdHelper,
  maxInterstitialAdHelper: MaxInterstitialAdHelper,
  appCoroutineScope: AppCoroutineScope,
) : ViewModel(), ContainerHost<CheckInViewState, CheckInSideEffect> {

  override val container: Container<CheckInViewState, CheckInSideEffect> =
    container(CheckInViewState())

  init {
    appCoroutineScope.launch(Dispatchers.Default) {
      maxInterstitialAdHelper.tryToLoadAd()
      maxRewardedAdHelper.tryToLoadAd()
    }

    logEventRecord("show_dialog_sign_in")

    rewardedLoadingDialogTimeoutEventFlow.onEach {
      debugLog("rewardedLoadingDialogTimeoutEventFlow.onEach")
      doClickBoostPointsMultiply(false)
    }.launchIn(viewModelScope)

    rewardedAdFinishEventFlow.onEach {
      intent {
        val checkInType = pointsComponentCache.todayCheckInType.first()
        if (checkInType == null) {
          postSideEffect(CheckInSideEffect.Toast("An exception occurred in the sign-in reward"))
          doClickBoostPointsMultiply(false)
          return@intent
        }

        reduce { state.copy(processing = true) }

        val pointResult = pointApi
          .addPoint(
            Transceiver(
              AddPointRequest(
                type = checkInType,
                times = 5 - 1
              )
            )
          )
          .safeFirst()
          ?.jsonData
          ?.data

        if (pointResult == null) {
          postSideEffect(CheckInSideEffect.Toast("Please check your network connectivity"))
          reduce { state.copy(processing = false, hasClickBoostPointsMultiply = false) }
        } else {
          pointsComponentCache.points.emit(pointResult.point)
          reduce { state.copy(processing = false, boostPointsMultiplySuccessful = true) }
        }
      }
    }.launchIn(viewModelScope)
  }

  fun onClickBoostPointsMultiply() = intent {
    doClickBoostPointsMultiply()
    maxRewardedAdHelper.tryToShowAdWithLoadingDialog(
      onAdLimit = {
        doClickBoostPointsMultiply(false)
      }
    )
  }

  private fun doClickBoostPointsMultiply(clicked: Boolean = true) = intent {
    reduce {
      state.copy(hasClickBoostPointsMultiply = clicked)
    }
  }
}