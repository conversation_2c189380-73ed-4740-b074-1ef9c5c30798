@file:Suppress("ObjectPropertyName")

package com.cchange.ai.friend.ad.appopen

import android.app.Activity
import android.content.Context
import com.applovin.mediation.MaxAd
import com.applovin.mediation.MaxAdListener
import com.applovin.mediation.MaxAdRevenueListener
import com.applovin.mediation.MaxError
import com.applovin.mediation.ads.MaxAppOpenAd
import com.cchange.ai.friend.BuildConfig
import com.cchange.ai.friend.ad.AdsConf
import com.cchange.ai.friend.ad.AppOpenSplashController
import com.cchange.ai.friend.ad.FullScreenAdLimitHelper
import com.cchange.ai.friend.biz.analysis.AnalysisLogger
import com.cchange.ai.friend.biz.analysis.logEventAdRevenueRecord
import com.cchange.ai.friend.biz.analysis.logEventRecord
import com.cchange.ai.friend.biz.remoteconf.FirebaseRemoteConf
import com.cchange.ai.friend.core.coroutine.AppCoroutineScope
import com.cchange.ai.friend.core.datetime.nowInstant
import com.cchange.ai.friend.core.flow.EventFlow
import com.cchange.ai.friend.core.flow.send
import com.cchange.ai.friend.core.flow.sendBlock
import com.cchange.ai.friend.core.log.debugLog
import com.cchange.ai.friend.data.repo.UserRepo
import com.google.firebase.analytics.FirebaseAnalytics
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.datetime.Instant
import java.util.UUID
import kotlin.time.DurationUnit
import kotlin.time.toDuration


data class CloseSplashEvent(val eventId: String = UUID.randomUUID().toString())

val closeSplashEventFlow: EventFlow<CloseSplashEvent> = EventFlow()

private const val TAG = "MaxAppOpenAdHelper"


sealed interface TryToShowExecuteResult {
  data object DoNotShow : TryToShowExecuteResult
  data object ReadyToShow : TryToShowExecuteResult
  data object Showing : TryToShowExecuteResult
  data object ShowFinish : TryToShowExecuteResult
  data object Error : TryToShowExecuteResult
}

class MaxAppOpenAdHelper(
  private val context: Context,
  private val splashController: AppOpenSplashController,
  private val appCoroutineScope: AppCoroutineScope,
  private val userRepo: UserRepo,
) {
  private val adConf
    get() = FirebaseRemoteConf.adsConf()?.app_open_ad_conf ?: AdsConf.Default.app_open_ad_conf

  private val _adCacheDuration = 4.toDuration(DurationUnit.HOURS)

  private var _appOpenAd: MaxAppOpenAd? = null

  private val _lastCacheAdInstant = MutableStateFlow(Instant.fromEpochSeconds(0))
  private val _isLoadingAdFlow = MutableStateFlow(false)

  val tryToShowExecuteResultEventFlow = EventFlow<TryToShowExecuteResult?>()

  private val maxAdOpenAdListener = object : MaxAdListener, MaxAdRevenueListener {
    override fun onAdLoaded(p0: MaxAd) {
      debugLog("$TAG maxAdOpenAdListener onAdLoaded")
      _isLoadingAdFlow.update { false }
      closeSplashEventFlow.sendBlock {
        CloseSplashEvent().apply {
          debugLog("loadAd send closeSplashEvent when onAdLoaded(): $this")
        }
      }
      _lastCacheAdInstant.update { nowInstant() }
    }

    override fun onAdDisplayed(p0: MaxAd) {
      debugLog("$TAG maxAdOpenAdListener onAdDisplayed")

      tryToShowExecuteResultEventFlow.send(TryToShowExecuteResult.Showing)
      splashController.skipSplash(true)

      debugLog("ad_app_open_impress")
      logEventRecord("ad_app_open_impress")
    }

    override fun onAdHidden(ad: MaxAd) {
      debugLog("$TAG maxAdOpenAdListener onAdHidden")

      tryToShowExecuteResultEventFlow.send(TryToShowExecuteResult.ShowFinish)
      splashController.skipSplash(false)

      debugLog("ad_app_open_close")
      logEventRecord("ad_app_open_close")

      loadAppOpenAd()
    }

    override fun onAdClicked(ad: MaxAd) {
      debugLog("ad_app_open_click")
      logEventRecord("ad_app_open_click")
      splashController.skipSplash(true)
    }

    override fun onAdLoadFailed(p0: String, p1: MaxError) {
      debugLog("$TAG maxAdOpenAdListener onAdLoadFailed: $p1")

      debugLog("ad_app_open_load_failed")
      logEventRecord("ad_app_open_load_failed")

      _isLoadingAdFlow.update { false }
    }

    override fun onAdDisplayFailed(p0: MaxAd, p1: MaxError) {
      debugLog("$TAG maxAdOpenAdListener onAdDisplayFailed")

      debugLog("ad_app_open_display_failed")
      logEventRecord("ad_app_open_display_failed")
      tryToShowExecuteResultEventFlow.send(TryToShowExecuteResult.Error)

    }

    override fun onAdRevenuePaid(ad: MaxAd) {
      if (!BuildConfig.DEBUG) {
        ad.let {
          logEventAdRevenueRecord("Ad_Impression_Revenue") {
            putDouble(FirebaseAnalytics.Param.VALUE, ad.revenue)
            putString(FirebaseAnalytics.Param.CURRENCY, "USD")
            putString("adNetwork", ad.networkName)
            putString("adFormat", ad.format.label)
          }

          AnalysisLogger.tryToRecordTotalAdsRevenue001(
            adFormat = ad.format.label,
            adValue = ad.revenue,
            adNetwork = ad.networkName,
            adUnitId = ad.adUnitId
          )

          logEventAdRevenueRecord(FirebaseAnalytics.Event.AD_IMPRESSION) {
            putString(FirebaseAnalytics.Param.AD_PLATFORM, "appLovin")
            putString(FirebaseAnalytics.Param.AD_SOURCE, ad.networkName)
            putString(FirebaseAnalytics.Param.AD_FORMAT, ad.format.displayName)
            putString(FirebaseAnalytics.Param.AD_UNIT_NAME, ad.adUnitId)
            putDouble(FirebaseAnalytics.Param.VALUE, ad.revenue)
            putString(FirebaseAnalytics.Param.CURRENCY, "USD") // All
          }

//          AnalysisLogger.roasReport("appOpen", ad)

          AnalysisLogger.logAdViewEvent(context, ad)

          AnalysisLogger.biLogEventAdOnPaid(
            value = ad.revenue.toFloat(),
            currency = "USD",
            precisionType = ad.revenuePrecision,
            adNetwork = ad.networkName ?: "",
            adType = ad.format.label ?: "",
          )
        }
      }
    }

  }

  fun initIfNeed(activity: Activity) {
    if (_appOpenAd != null) return

    appCoroutineScope.launch(Dispatchers.Default) {
      if (userRepo.currentLoginUser().find()?.isSubsActive == true) {
        return@launch
      } else {
        launch(Dispatchers.Main.immediate) {
          if (_appOpenAd == null) {
            debugLog("$TAG init")
            _appOpenAd = MaxAppOpenAd(adConf.ad_id, activity).apply {
              setListener(maxAdOpenAdListener)
              setRevenueListener(maxAdOpenAdListener)
            }
            loadAppOpenAd()
          }
        }
      }
    }
  }

  private fun loadAppOpenAd() {
    appCoroutineScope.launch(Dispatchers.Default) {
      if (userRepo.currentLoginUser().find()?.isSubsActive == true) {
        return@launch
      } else {
        launch(Dispatchers.Main.immediate) {
          _appOpenAd?.let {
            debugLog("$TAG loadAppOpenAd()")
            it.loadAd()
            _isLoadingAdFlow.update { true }
          }
        }
      }
    }
  }

  private fun showAppOpenAd() {
    _appOpenAd?.showAd()
    debugLog("$TAG _appOpenAd?.showAd()")
  }

  suspend fun tryToLoadAd() {
    debugLog("$TAG tryToLoadAd()")

    if (FullScreenAdLimitHelper.isLimit()) {
      debugLog("loadAd tryToLoadRewardedAd failed")
      debugLog("loadAd FullScreenAdLimitHelper.isLimit()")
      return
    }

    appCoroutineScope.launch(Dispatchers.Main) {
      if (userRepo.currentLoginUser().find()?.isSubsActive == true) {
        debugLog("loadAd tryToLoadRewardedAd failed")
        debugLog("loadAd isSubsActive")
        return@launch
      }

      val now = nowInstant()

      if (
        _appOpenAd?.isReady == true
        && now - _adCacheDuration < _lastCacheAdInstant.first()
      ) { // has available ad cache
        debugLog("$TAG tryToLoadAd() has available ad cache")

        closeSplashEventFlow.sendBlock {
          CloseSplashEvent().apply {
            debugLog("loadAd send closeSplashEvent when has ad cache: $this")
          }
        }
      } else {
        val isLoadingAd = _isLoadingAdFlow.first()
        debugLog("$TAG tryToLoadAd() isLoadingAd: $isLoadingAd")

        if (!isLoadingAd) {
          loadAppOpenAd()
        }

        launch {
          instantLoadTimeoutDelay()
          if (_appOpenAd?.isReady != true) {
            closeSplashEventFlow.sendBlock {
              CloseSplashEvent().apply {
                debugLog("loadAd send closeSplashEvent when timeout: $this")
              }
            }
          }
        }
      }
    }
  }

  fun tryToShowAd() {
    debugLog("$TAG tryToShowAd()")
    appCoroutineScope.launch(Dispatchers.Main.immediate) {
      if (userRepo.currentLoginUser().find()?.isSubsActive == true) {
        delay(1_000)
        tryToShowExecuteResultEventFlow.send(TryToShowExecuteResult.DoNotShow)
        return@launch
      }

      if (FullScreenAdLimitHelper.isLimit()) {
        debugLog("loadAd tryToShowRewardedLoadingDialog failed")
        debugLog("loadAd FullScreenAdLimitHelper.isLimit()")
        delay(2_000)
        tryToShowExecuteResultEventFlow.send(TryToShowExecuteResult.DoNotShow)

        return@launch
      }

      debugLog("ad_app_open_show")
      logEventRecord("ad_app_open_show")

      val now = nowInstant()

      if (
        _appOpenAd?.isReady == true
        && now - _adCacheDuration < _lastCacheAdInstant.first()
      ) {
        debugLog("$TAG cache available invoke showAppOpenAd()")
        tryToShowExecuteResultEventFlow.send(TryToShowExecuteResult.ReadyToShow)
        delay(2_000)

        withContext(Dispatchers.Main.immediate) {
          showAppOpenAd()
        }
      } else {
        tryToShowExecuteResultEventFlow.send(TryToShowExecuteResult.DoNotShow)

        val isLoadingAd = _isLoadingAdFlow.first()
        debugLog("$TAG cache unavailable isLoadingAd: $isLoadingAd")
        if (!isLoadingAd) {
          loadAppOpenAd()
        }
      }
    }
  }

  suspend fun instantLoadTimeoutDelay() {
    if (FullScreenAdLimitHelper.isLimit()) {
      delay(2_000)
    } else {
      delay(adConf.ad_instant_load_timeout_seconds.toDuration(DurationUnit.SECONDS))
    }
  }
}
