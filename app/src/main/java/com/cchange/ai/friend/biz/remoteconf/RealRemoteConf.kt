package com.cchange.ai.friend.biz.remoteconf

import com.cchange.ai.friend.ad.AdsConf
import com.cchange.ai.friend.ad.AdsConfA
import com.cchange.ai.friend.ad.AdsConfTp
import com.cchange.ai.friend.ad.AdsProvider
import com.cchange.ai.friend.biz.noti.repeat.RemoteConfigMessageRepeatNotification
import com.cchange.ai.friend.biz.noti.repeat.RepeatNotiGroup
import com.cchange.ai.friend.biz.noti.repeat.RepeatNotiPushStrategy

class RealRemoteConf(
  private val biApiRemoteConf: BiApiRemoteConf,
) {
  private val firebaseRemoteConf: FirebaseRemoteConf = FirebaseRemoteConf

  suspend fun adsConf(): AdsConf? {
    return biApiRemoteConf.adsConf() ?: firebaseRemoteConf.adsConf()
  }

  suspend fun repeatNotiPushStrategy(
    repeatNotification: RemoteConfigMessageRepeatNotification
  ): RepeatNotiPushStrategy {
    return biApiRemoteConf.repeatNotiPushStrategy(repeatNotification)
      ?: firebaseRemoteConf.repeatNotiPushStrategy(repeatNotification)
  }

  suspend fun repeatNotiGroup(
    repeatNotification: RemoteConfigMessageRepeatNotification
  ): RepeatNotiGroup {
    return biApiRemoteConf.repeatNotiGroup(repeatNotification)
      ?: firebaseRemoteConf.repeatNotiGroup(repeatNotification)
  }

  suspend fun adsProvider(): AdsProvider {
    return biApiRemoteConf.adsProvider() ?: firebaseRemoteConf.adsProvider()
  }

  suspend fun adsConfAdmob(): AdsConfA {
    return biApiRemoteConf.adsConfAdmob() ?: firebaseRemoteConf.adsConfAdmob()
  }

  suspend fun adsConfTp(): AdsConfTp {
    return biApiRemoteConf.adsConfTp() ?: firebaseRemoteConf.adsConfTp()
  }
}