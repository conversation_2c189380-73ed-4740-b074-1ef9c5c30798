//package com.cchange.ai.friend.biz.bi
//
//import com.cchange.ai.friend.biz.remoteconf.FirebaseRemoteConf
//import com.cchange.ai.friend.core.coroutine.AppCoroutineScope
//import com.cchange.ai.friend.core.flow.safeFirst
//import com.cchange.ai.friend.data.network.pojo.Transceiver
//import com.cchange.ai.friend.data.network.pojo.isSuccess
//import kotlinx.coroutines.Dispatchers
//import kotlinx.coroutines.delay
//import kotlinx.coroutines.launch
//import org.koin.core.context.GlobalContext
//import kotlin.time.DurationUnit
//import kotlin.time.toDuration
//
//fun BiApi.registerUpdateUserGroup() {
//  val appCoroutineScope = GlobalContext.get().get<AppCoroutineScope>()
//
//  var updateSuccess = false
//
//  appCoroutineScope.launch(Dispatchers.Default) rootLaunch@{
//    repeat(20) {
//      FirebaseRemoteConf.fetch {
//        appCoroutineScope.launch(Dispatchers.Default) {
//          val realAbTestId = FirebaseRemoteConf.realAbTestId()
//
//          if (realAbTestId != null) {
//            updateSuccess = updateUserGroup(Transceiver(UpdateUserGroupRequest(realAbTestId)))
//              .safeFirst()
//              .isSuccess()
//          }
//        }
//      }
//
//      delay(3.toDuration(DurationUnit.MINUTES))
//
//      if (updateSuccess) return@rootLaunch
//    }
//  }
//}