package com.cchange.ai.friend.ui.screen.home

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.cchange.ai.friend.ad.interstitial.MaxInterstitialAdHelper
import com.cchange.ai.friend.ad.interstitial.MaxInterstitialAdShowEvent
import com.cchange.ai.friend.core.coroutine.AppCoroutineScope
import com.cchange.ai.friend.ui.dialog.point.checkin.CheckInSideEffect
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.viewmodel.container

val homeNavigationSelectedStateFlow = MutableStateFlow<SubScreen?>(null)

class HomeViewModel(
  args: HomeArgs,
  private val maxInterstitialAdHelper: MaxInterstitialAdHelper,
  appCoroutineScope: AppCoroutineScope,
) : ViewModel(), ContainerHost<HomeViewState, Nothing> {

  override val container: Container<HomeViewState, Nothing> = container(HomeViewState())

  init {
    intent {
      reduce {
        state.copy(selectedSubScreen = args.subScreen)
      }
    }

    appCoroutineScope.launch(Dispatchers.Default) {
      maxInterstitialAdHelper.tryToLoadAd()
    }
  }

  fun onConfigure(coroutineScope: CoroutineScope) {
//    maxInterstitialAdHelper.registerAutoNavUp(coroutineScope)
  }

  fun onNavigationSelected(subScreen: SubScreen) = intent {
    reduce { state.copy(selectedSubScreen = subScreen) }
    homeNavigationSelectedStateFlow.update { subScreen }
  }

  override fun onCleared() {
    homeNavigationSelectedStateFlow.update { null }
    super.onCleared()
  }

}