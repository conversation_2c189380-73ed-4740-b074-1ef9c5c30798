import com.google.firebase.crashlytics.buildtools.gradle.CrashlyticsExtension
import java.util.Properties

plugins {
  alias(libs.plugins.compose.compiler)
  alias(libs.plugins.androidApplication)
  alias(libs.plugins.jetbrainsKotlinAndroid)
  alias(libs.plugins.kotlin.parcelize)
  alias(libs.plugins.kotlin.serialization)
  alias(libs.plugins.ktorfit)
  alias(libs.plugins.ksp)
  alias(libs.plugins.google.services)
  alias(libs.plugins.firebase.crashlytics)
  id(libs.versions.krdbId.get())
//  id("applovin-quality-service")
}


apply(from = "${rootProject.projectDir}/conf4build/copyProject.gradle")
apply(from = "${rootProject.projectDir}/conf4build/copyAabWhenBundleFinish.gradle")

val confProp = Properties()
file("${rootProject.projectDir}/conf4build/buildConf.properties").inputStream().use {
  confProp.load(it)
}

//applovin {
//  apiKey = "l9fF_fDfyvSZ1eZmETn9o6CKFAhbeUX3cGPn7BTKLb7Knm_Z5qqCKunWU03akik2UHewkTCoIl779pTxcXKl0P"
//}

android {
  namespace = "com.cchange.ai.friend"
  compileSdk = 36

  defaultConfig {
    applicationId = "com.cchange.ai.friend"
    minSdk = 23
    targetSdk = 34
    versionCode = 666
    versionName = "6.6.6"

    testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
    vectorDrawables {
      useSupportLibrary = true
    }

    ndk {
      abiFilters.addAll(setOf("arm64-v8a", "armeabi-v7a", "x86", "x86_64"))
    }
  }


  signingConfigs {
//    getByName("debug") {
//      storeFile = rootProject.file("release/app-debug.jks")
//      storePassword = "android"
//      keyAlias = "androiddebugkey"
//      keyPassword = "android"
//    }
//
//    create("release") {
//      if (rootProject.file("release/cchange").exists()) {
//        storeFile = rootProject.file("release/cchange")
//        storePassword = properties["IAI_RELEASE_KEYSTORE_PWD"]?.toString() ?: ""
//        keyAlias = "cchange"
//        keyPassword = properties["IAI_RELEASE_KEYSTORE_PWD"]?.toString() ?: ""
//      }
//    }

    create("internal") {
      storeFile = rootProject.file("test_sign")
      storePassword = "test_sign"
      keyAlias = "test_sign"
      keyPassword = "test_sign"
    }
  }

  buildTypes {
//      debug {
//        signingConfig = signingConfigs["debug"]
//        versionNameSuffix = "-dev"
//        applicationIdSuffix = ".debug"
//      }
//
//    release {
//      signingConfig = signingConfigs.findByName("release") ?: signingConfigs["debug"]
//      isShrinkResources = true
//      isMinifyEnabled = true
//      proguardFiles("proguard-rules.pro")
//      configure<CrashlyticsExtension> {
//        mappingFileUploadEnabled = false
//      }
//    }

    create("internal") {
      isMinifyEnabled = true
      isShrinkResources = true
      signingConfig = signingConfigs.getByName("internal")
      proguardFiles(getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro")
      configure<CrashlyticsExtension> {
        mappingFileUploadEnabled = false
      }
    }
  }

  compileOptions {
    isCoreLibraryDesugaringEnabled = true
    sourceCompatibility = JavaVersion.VERSION_17
    targetCompatibility = JavaVersion.VERSION_17
  }
  kotlin {
    jvmToolchain(17)
    sourceSets {
      all {
        languageSettings {
          optIn("com.russhwolf.settings.ExperimentalSettingsApi")
          optIn("androidx.compose.material3.ExperimentalMaterial3Api")
          optIn("androidx.compose.foundation.ExperimentalFoundationApi")
          optIn("kotlinx.coroutines.DelicateCoroutinesApi")
          optIn("org.orbitmvi.orbit.annotation.OrbitExperimental")
          optIn("androidx.compose.foundation.layout.ExperimentalLayoutApi")
          optIn("androidx.compose.ui.text.ExperimentalTextApi")
          optIn("kotlinx.serialization.ExperimentalSerializationApi")
        }
      }
    }
  }
  buildFeatures {
    compose = true
    buildConfig = true
  }
  composeOptions {
//    kotlinCompilerExtensionVersion = libs.versions.composeCompiler.get()
  }
  packaging {
    resources {
      excludes += "/META-INF/{AL2.0,LGPL2.1}"
    }
  }
}

dependencies {
  coreLibraryDesugaring(libs.desugar.jdk.libs)

  implementation(libs.androidx.core.ktx)
  implementation(libs.androidx.startup.runtime)
  implementation(libs.androidx.lifecycle.runtime.ktx)
  implementation(libs.androidx.lifecycle.runtime.compose)
  implementation(libs.androidx.lifecycle.viewmodel.ktx)
  implementation(libs.androidx.lifecycle.viewmodel.compose)
  implementation(libs.androidx.activity.compose)
  implementation(platform(libs.androidx.compose.bom))
  implementation(libs.androidx.ui)
  implementation(libs.androidx.ui.graphics)
  implementation(libs.androidx.ui.tooling.preview)
  implementation(libs.androidx.material.icons.extended)
  implementation(libs.androidx.material3)
//  implementation(libs.androidx.ui.text.google.fonts)

  implementation(libs.resaca)
  implementation(libs.resacakoin)

  implementation(platform(libs.koin.bom))
  implementation(libs.koin.core)
  implementation(libs.koin.android)
  implementation(libs.koin.androidx.compose)

  testImplementation(libs.junit)
  androidTestImplementation(libs.androidx.junit)
  androidTestImplementation(libs.androidx.espresso.core)
  androidTestImplementation(platform(libs.androidx.compose.bom))
  androidTestImplementation(libs.androidx.ui.test.junit4)
  debugImplementation(libs.androidx.ui.tooling)
  debugImplementation(libs.androidx.ui.test.manifest)

  implementation(libs.krdb.base)
  implementation(libs.kache)

  implementation(libs.kotlinx.coroutines.core)
  implementation(libs.kotlinx.coroutines.android)
  implementation(libs.kotlinx.datetime)
  implementation(libs.kotlinx.serialization.json)

  implementation(libs.kronos)
  implementation(libs.multiplatform.settings)
//  implementation(libs.multiplatform.settings.noarg)
//  implementation(libs.multiplatform.settings.datastore)
  implementation(libs.multiplatform.settings.coroutines)
//  implementation(libs.multiplatform.settings.serialization)

  implementation(libs.guia)
  implementation(libs.multiplatform.markdown.renderer.m3)
  implementation(libs.multiplatform.markdown.renderer.coil2)
  implementation(libs.multiplatform.markdown.renderer.code)

  implementation(libs.bottomsheetdialog.compose)

//  implementation(libs.compose.settings.ui.m3)
  implementation(libs.toasty)
  implementation(libs.lottie.compose)

  implementation(libs.lyricist)
  ksp(libs.lyricist.processor)

  implementation(libs.orbit.viewmodel)
  implementation(libs.orbit.compose)

  implementation(libs.kermit)

  implementation(libs.coil.compose)
  implementation(libs.compressor)

  implementation(libs.zoomable.image.coil)

  ksp(libs.ktorfit.ksp)
  implementation(libs.ktorfit.lib.light)
  implementation(libs.ktorfit.converters.response)
  implementation(libs.ktor.client.okhttp)
  implementation(libs.ktor.client.content.negotiation)
  implementation(libs.ktor.serialization.kotlinx.json)

  implementation(libs.commons.codec)
//  implementation(libs.commons.io)


  // Credential Manager
  implementation(libs.androidx.credentials)
  implementation(libs.androidx.credentials.playServicesAuth)
  implementation(libs.googleIdIdentity)
  implementation(libs.android.legacy.playServicesAuth)


  implementation(platform(libs.firebase.bom))
  implementation(libs.firebase.crashlytics)
  implementation(libs.firebase.analytics)
  implementation(libs.firebase.config)
  implementation(libs.firebase.messaging)

  implementation(libs.review.ktx)

  implementation(libs.installreferrer)

  implementation(libs.billinghelper)

  implementation("com.applovin:applovin-sdk:13.0.1")
  implementation("com.appsflyer:af-android-sdk:6.16.0")
//  implementation("com.tenjin:android-sdk:1.16.4")


  implementation("com.google.code.gson:gson:2.13.1")

  // Google Ads (Admob)
  implementation("com.google.android.gms:play-services-ads:24.4.0")

  // TradPlus
  implementation("com.tradplusad:tradplus:14.3.30.1")
}