# This is a configuration file for R8
#-verbose
#-allowaccessmodification
#-repackageclasses
#
-keepclasseswithmembernames class * {
    native <methods>;
}

# Android
-keep class * implements androidx.viewbinding.ViewBinding {*;}

-keepclassmembers public class * extends androidx.lifecycle.ViewModel {
    public <init>(...);
}

# We only need to keep ComposeView
#-keep public class androidx.compose.ui.platform.ComposeView {
#    public <init>(android.content.Context, android.util.AttributeSet);
#}

# For enumeration classes
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# Parcelable
#-keep class * implements android.os.Parcelable {
#  public static final android.os.Parcelable$Creator *;
#}

# Credential Manager
-if class androidx.credentials.CredentialManager
-keep class androidx.credentials.playservices.** {
  *;
}

# Kotlinx Serialization
-keep @kotlinx.serialization.Serializable class * {*;}

# IAI Pojo/ADT
-keep class com.cchange.ai.friend.data.adt.** {*;}
-keep class com.cchange.ai.friend.data.network.pojo.** {*;}

# AndroidX + support library contains references to newer platform versions.
# Don't warn about those in case this app is linking against an older
# platform version.  We know about them, and they are safe.
-dontwarn android.support.**
-dontwarn androidx.**

#-keepattributes SourceFile,
#                LineNumberTable,
#                RuntimeVisibleAnnotations,
#                RuntimeVisibleParameterAnnotations,
#                RuntimeVisibleTypeAnnotations,
#                AnnotationDefault
#
#-renamesourcefileattribute SourceFile


# See https://issuetracker.google.com/issues/265188224
-keep,allowshrinking class * extends androidx.compose.ui.node.ModifierNodeElement {}

# Using ktor client in Android has missing proguard rule
# See https://youtrack.jetbrains.com/issue/KTOR-5528
-dontwarn org.slf4j.**


################################### Kotlin Coroutines ##############################################
# Kotlin Coroutines
# ServiceLoader support
-keepnames class kotlinx.coroutines.internal.MainDispatcherFactory {}
-keepnames class kotlinx.coroutines.CoroutineExceptionHandler {}

# Most of volatile fields are updated with AFU and should not be mangled
-keepclassmembers class kotlinx.coroutines.** {
    volatile <fields>;
}

# Same story for the standard library's SafeContinuation that also uses AtomicReferenceFieldUpdater
-keepclassmembers class kotlin.coroutines.SafeContinuation {
    volatile <fields>;
}

# These classes are only required by kotlinx.coroutines.debug.AgentPremain, which is only loaded when
# kotlinx-coroutines-core is used as a Java agent, so these are not needed in contexts where ProGuard is used.
-dontwarn java.lang.instrument.ClassFileTransformer
-dontwarn sun.misc.SignalHandler
-dontwarn java.lang.instrument.Instrumentation
-dontwarn sun.misc.Signal

# OKIO
# Animal Sniffer compileOnly dependency to ensure APIs are compatible with older versions of Java.
-dontwarn org.codehaus.mojo.animal_sniffer.*

# OKHTTP
# JSR 305 annotations are for embedding nullability information.
-dontwarn javax.annotation.**

# A resource is loaded with a relative path so the package of this class must be preserved.
-keepnames class okhttp3.internal.publicsuffix.PublicSuffixDatabase

# Animal Sniffer compileOnly dependency to ensure APIs are compatible with older versions of Java.
-dontwarn org.codehaus.mojo.animal_sniffer.*

# OkHttp platform used only on JVM and when Conscrypt dependency is available.
-dontwarn okhttp3.internal.platform.ConscryptPlatform
-dontwarn org.conscrypt.ConscryptHostnameVerifier

##################################### ktorfit #####################################################
-keep class de.jensklingenberg.ktorfit.** { *; }
-keepclassmembers class de.jensklingenberg.ktorfit.** { *; }

###################################################################################################

-keep class kotlinx.coroutines.flow.Flow
## 保留Kotlin反射相关的类和方法
#-keep class io.ktor.util.reflect.** { *; }
#-keep class kotlin.reflect.** { *; }
#-keepclassmembers class io.ktor.util.reflect.** { *; }
#-keepclassmembers class kotlin.reflect.** { *; }
#
## 如果使用了Ktor客户端，保留相关的类和方法
#-keep class io.ktor.client.** { *; }

################################### tenjin ##############################################
-keep class com.tenjin.** { *; }
-keep public class com.google.android.gms.ads.identifier.** { *; }
-keep public class com.google.android.gms.common.** { *; }
-keep public class com.android.installreferrer.** { *; }
-keep class * extends java.util.ListResourceBundle {
    protected java.lang.Object[][] getContents();
}
-keepattributes *Annotation*

#################################### mtg ###################################################

-keepattributes Signature
-keepattributes *Annotation*
-keep class com.mbridge.** {*; }
-keep interface com.mbridge.** {*; }
-dontwarn com.mbridge.**
-keepclassmembers class **.R$* { public static final int mbridge*; }

-keep public class com.mbridge.* extends androidx.** { *; }
-keep public class androidx.viewpager.widget.PagerAdapter{*;}
-keep public class androidx.viewpager.widget.ViewPager.OnPageChangeListener{*;}
-keep interface androidx.annotation.IntDef{*;}
-keep interface androidx.annotation.Nullable{*;}
-keep interface androidx.annotation.CheckResult{*;}
-keep interface androidx.annotation.NonNull{*;}
-keep public class androidx.fragment.app.Fragment{*;}
-keep public class androidx.core.content.FileProvider{*;}
-keep public class androidx.core.app.NotificationCompat{*;}
-keep public class androidx.appcompat.widget.AppCompatImageView {*;}
-keep public class androidx.recyclerview.*{*;}
-keep class com.mbridge.msdk.foundation.tools.FastKV{*;}
-keep class com.mbridge.msdk.foundation.tools.FastKV$Builder{*;}

############################ Google Play Install Referrer #####################################
-keep public class com.android.installreferrer.** { *; }

#################################### appsflyer #################################################
-keep class com.appsflyer.** { *; }

#################################### Billing Helper #################################################
-keep class com.vojtkovszky.billinghelper.** { *; }

################################### Admob ##############################################
-keep class com.google.android.gms.ads.** { *; }
-dontwarn com.google.android.gms.ads.**

################################ tradplus ###############################################
-keep public class com.tradplus.** { *; }
-keep class com.tradplus.ads.** { *; }

######################################### GSON #################################################

###---------------Begin: proguard configuration for Gson  ----------
# Gson uses generic type information stored in a class file when working with fields. Proguard
# removes such information by default, so configure it to keep all of it.
-keepattributes Signature

# For using GSON @Expose annotation
-keepattributes *Annotation*

# Gson specific classes
-dontwarn sun.misc.**
-keep class com.google.gson.stream.** { *; }
-keep class com.google.gson.** { *; }

# Application classes that will be serialized/deserialized over Gson
-keep class com.google.gson.examples.android.model.** { <fields>; }

# Prevent proguard from stripping interface information from TypeAdapter, TypeAdapterFactory,
# JsonSerializer, JsonDeserializer instances (so they can be used in @JsonAdapter)
-keep class * extends com.google.gson.TypeAdapter
-keep class * implements com.google.gson.TypeAdapterFactory
-keep class * implements com.google.gson.JsonSerializer
-keep class * implements com.google.gson.JsonDeserializer

# Prevent R8 from leaving Data object members always null
-keepclassmembers,allowobfuscation class * {
  @com.google.gson.annotations.SerializedName <fields>;
}

# Retain generic signatures of TypeToken and its subclasses with R8 version 3.0 and higher.
-keep,allowobfuscation,allowshrinking class com.google.gson.reflect.TypeToken
-keep,allowobfuscation,allowshrinking class * extends com.google.gson.reflect.TypeToken

###---------------End: proguard configuration for Gson  ----------

################################ other ###############################################
-dontwarn com.google.gson.Gson
-dontwarn com.google.gson.reflect.TypeToken
